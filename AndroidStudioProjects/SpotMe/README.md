# SpotMe - Location-Based Social Discovery App

SpotMe is a location-based social discovery app that allows users to discover and interact with people within 300 meters of their location. Built with Kotlin, Jetpack Compose, and Firebase.

## Features

### ✅ Implemented Core Features

1. **User Authentication**
   - Email/password registration and login
   - Google Sign-In integration (placeholder)
   - Password reset functionality
   - Secure Firebase Authentication

2. **User Profile Management**
   - Complete profile setup with photo, name, gender, birth date
   - Bio and interests customization
   - Age calculation from birth date
   - Profile visibility controls (invisible mode)

3. **Location Services**
   - Real-time GPS location tracking
   - Location permission handling
   - Privacy controls for location sharing
   - 300-meter discovery radius

4. **User Discovery**
   - Find nearby users within 300 meters
   - List view with user cards
   - Distance calculation and display
   - Filter by gender, age, and interests
   - Refresh functionality

5. **Modern UI/UX**
   - Material Design 3 components
   - Jetpack Compose UI
   - Responsive design
   - Dark/light theme support

### 🚧 Features to be Implemented

1. **Real-time Chat System**
   - Text messaging
   - Image sharing
   - Push notifications
   - Chat history

2. **Map View**
   - Google Maps integration
   - User markers on map
   - Interactive map navigation

3. **Social Features**
   - Like/friend request system
   - Block/report functionality
   - User verification

4. **Enhanced Discovery**
   - Advanced filtering options
   - Search functionality
   - Favorite users

## Tech Stack

- **Language**: Kotlin
- **UI Framework**: Jetpack Compose
- **Architecture**: MVVM with Repository pattern
- **Backend**: Firebase (Auth, Firestore, Storage, Cloud Messaging)
- **Location**: Google Play Services Location
- **Image Loading**: Coil
- **Navigation**: Navigation Compose
- **Permissions**: Accompanist Permissions

## Project Structure

```
app/src/main/java/com/example/spotme/
├── data/
│   ├── model/          # Data models (User, Message, Location)
│   └── repository/     # Repository classes for data management
├── ui/
│   ├── screen/         # Compose screens
│   ├── viewmodel/      # ViewModels
│   └── theme/          # UI theme and styling
├── navigation/         # Navigation setup
└── MainActivity.kt     # Main activity
```

## Setup Instructions

### 1. Firebase Setup

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Create a new project or use existing one
3. Add an Android app with package name: `com.example.spotme`
4. Download the `google-services.json` file
5. Replace the placeholder `google-services.json` in the `app/` directory

### 2. Enable Firebase Services

In your Firebase project, enable:
- **Authentication** (Email/Password, Google)
- **Cloud Firestore** (Database)
- **Cloud Storage** (File storage)
- **Cloud Messaging** (Push notifications)

### 3. Firestore Security Rules

Set up Firestore security rules:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow read: if request.auth != null && resource.data.isVisible == true;
    }
    
    // Messages can be read/written by participants
    match /messages/{messageId} {
      allow read, write: if request.auth != null && 
        (request.auth.uid == resource.data.senderId || 
         request.auth.uid == resource.data.receiverId);
    }
    
    // Chats can be read/written by participants
    match /chats/{chatId} {
      allow read, write: if request.auth != null && 
        request.auth.uid in resource.data.participants;
    }
  }
}
```

### 4. Storage Security Rules

Set up Storage security rules:

```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /profile_images/{userId}.jpg {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    match /message_images/{imageId} {
      allow read, write: if request.auth != null;
    }
  }
}
```

### 5. Google Maps API (for future map feature)

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Enable Maps SDK for Android
3. Create an API key
4. Add the API key to your app

### 6. Build and Run

1. Open the project in Android Studio
2. Sync the project with Gradle files
3. Run the app on a device or emulator

## Permissions Required

- `ACCESS_FINE_LOCATION` - For precise location tracking
- `ACCESS_COARSE_LOCATION` - For approximate location
- `INTERNET` - For Firebase communication
- `CAMERA` - For taking profile photos
- `READ_EXTERNAL_STORAGE` - For selecting images
- `READ_MEDIA_IMAGES` - For Android 13+ image access

## Key Components

### Data Models

- **User**: Complete user profile with location and preferences
- **Message**: Chat messages with support for text and images
- **UserLocation**: Location data with distance calculations

### Repositories

- **AuthRepository**: Firebase Authentication management
- **UserRepository**: User profile and data management
- **LocationRepository**: Location services and nearby user discovery
- **ChatRepository**: Real-time messaging functionality

### ViewModels

- **AuthViewModel**: Authentication state management
- **ProfileViewModel**: User profile management
- **DiscoveryViewModel**: Nearby user discovery and filtering

## Privacy and Security

- Location data is only shared when users opt-in
- Users can toggle visibility (invisible mode)
- Secure Firebase Authentication
- Data encryption in transit and at rest
- User blocking and reporting capabilities

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is for educational purposes. Please ensure you comply with all applicable laws and regulations when implementing location-based features.

## Support

For issues and questions, please create an issue in the repository.

---

**Note**: This is a functional implementation of the core features. The placeholder Firebase configuration needs to be replaced with your actual Firebase project configuration for the app to work properly.
