package com.example.spotme.data.repository

import android.net.Uri
import com.example.spotme.data.model.User
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.GeoPoint
import com.google.firebase.storage.FirebaseStorage
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.tasks.await
import java.util.*

class UserRepository {
    private val auth = FirebaseAuth.getInstance()
    private val firestore = FirebaseFirestore.getInstance()
    private val storage = FirebaseStorage.getInstance()
    
    private val usersCollection = firestore.collection("users")
    
    suspend fun getCurrentUser(): User? {
        val currentUserId = auth.currentUser?.uid ?: return null
        return try {
            val document = usersCollection.document(currentUserId).get().await()
            document.toObject(User::class.java)?.copy(id = document.id)
        } catch (e: Exception) {
            null
        }
    }
    
    suspend fun getUserById(userId: String): User? {
        return try {
            val document = usersCollection.document(userId).get().await()
            document.toObject(User::class.java)?.copy(id = document.id)
        } catch (e: Exception) {
            null
        }
    }
    
    suspend fun createUser(user: User): Result<User> {
        return try {
            val userId = auth.currentUser?.uid ?: throw Exception("User not authenticated")
            val userWithId = user.copy(
                id = userId,
                createdAt = Date(),
                updatedAt = Date()
            )
            usersCollection.document(userId).set(userWithId).await()
            Result.success(userWithId)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun updateUser(user: User): Result<User> {
        return try {
            val updatedUser = user.copy(updatedAt = Date())
            usersCollection.document(user.id).set(updatedUser).await()
            Result.success(updatedUser)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun updateUserLocation(location: GeoPoint): Result<Unit> {
        return try {
            val userId = auth.currentUser?.uid ?: throw Exception("User not authenticated")
            usersCollection.document(userId).update(
                mapOf(
                    "location" to location,
                    "lastLocationUpdate" to Date(),
                    "updatedAt" to Date()
                )
            ).await()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun uploadProfileImage(imageUri: Uri): Result<String> {
        return try {
            val userId = auth.currentUser?.uid ?: throw Exception("User not authenticated")
            val imageRef = storage.reference.child("profile_images/$userId.jpg")
            val uploadTask = imageRef.putFile(imageUri).await()
            val downloadUrl = uploadTask.storage.downloadUrl.await()
            Result.success(downloadUrl.toString())
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun setUserVisibility(isVisible: Boolean): Result<Unit> {
        return try {
            val userId = auth.currentUser?.uid ?: throw Exception("User not authenticated")
            usersCollection.document(userId).update(
                mapOf(
                    "isVisible" to isVisible,
                    "updatedAt" to Date()
                )
            ).await()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun setUserOnlineStatus(isOnline: Boolean): Result<Unit> {
        return try {
            val userId = auth.currentUser?.uid ?: throw Exception("User not authenticated")
            usersCollection.document(userId).update(
                mapOf(
                    "isOnline" to isOnline,
                    "updatedAt" to Date()
                )
            ).await()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    fun observeUser(userId: String): Flow<User?> = flow {
        try {
            usersCollection.document(userId).addSnapshotListener { snapshot, _ ->
                val user = snapshot?.toObject(User::class.java)?.copy(id = snapshot.id)
                // Note: In a real implementation, you'd use a proper Flow with callbacks
            }
        } catch (e: Exception) {
            emit(null)
        }
    }
}
