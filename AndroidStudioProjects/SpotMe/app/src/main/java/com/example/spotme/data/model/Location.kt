package com.example.spotme.data.model

import com.google.firebase.firestore.GeoPoint
import kotlin.math.*

data class UserLocation(
    val userId: String = "",
    val geoPoint: GeoPoint = GeoPoint(0.0, 0.0),
    val timestamp: Long = System.currentTimeMillis(),
    val accuracy: Float = 0f
) {
    fun distanceTo(other: UserLocation): Double {
        return calculateDistance(
            this.geoPoint.latitude,
            this.geoPoint.longitude,
            other.geoPoint.latitude,
            other.geoPoint.longitude
        )
    }
    
    companion object {
        // Calculate distance between two points using Haversine formula
        fun calculateDistance(lat1: Double, lon1: Double, lat2: Double, lon2: Double): Double {
            val earthRadius = 6371000.0 // Earth radius in meters
            
            val dLat = Math.toRadians(lat2 - lat1)
            val dLon = Math.toRadians(lon2 - lon1)
            
            val a = sin(dLat / 2).pow(2) + 
                    cos(Math.toRadians(lat1)) * cos(Math.toRadians(lat2)) * 
                    sin(dLon / 2).pow(2)
            
            val c = 2 * atan2(sqrt(a), sqrt(1 - a))
            
            return earthRadius * c
        }
        
        const val MAX_DISCOVERY_DISTANCE = 300.0 // 300 meters
    }
}
