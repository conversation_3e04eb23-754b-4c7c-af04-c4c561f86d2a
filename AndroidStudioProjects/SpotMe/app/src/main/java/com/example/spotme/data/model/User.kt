package com.example.spotme.data.model

import com.google.firebase.firestore.GeoPoint
import java.util.Date

data class User(
    val id: String = "",
    val email: String = "",
    val name: String = "",
    val profileImageUrl: String = "",
    val gender: Gender = Gender.OTHER,
    val birthDate: Date = Date(),
    val bio: String = "",
    val interests: List<String> = emptyList(),
    val location: GeoPoint? = null,
    val lastLocationUpdate: Date = Date(),
    val isVisible: Boolean = true,
    val isOnline: Boolean = false,
    val createdAt: Date = Date(),
    val updatedAt: Date = Date()
) {
    // Calculate age from birth date
    fun getAge(): Int {
        val currentDate = Date()
        val diffInMillis = currentDate.time - birthDate.time
        val ageInYears = diffInMillis / (365.25 * 24 * 60 * 60 * 1000)
        return ageInYears.toInt()
    }
    
    // Check if user has complete profile
    fun isProfileComplete(): <PERSON>olean {
        return name.isNotBlank() && 
               profileImageUrl.isNotBlank() && 
               gender != Gender.OTHER
    }
}

enum class Gender {
    MALE, FEMALE, OTHER
}
