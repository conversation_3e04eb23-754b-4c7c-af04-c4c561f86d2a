package com.example.spotme.ui.viewmodel

import android.net.Uri
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.spotme.data.model.Gender
import com.example.spotme.data.model.User
import com.example.spotme.data.repository.UserRepository
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.util.*

class ProfileViewModel(
    private val userRepository: UserRepository = UserRepository()
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(ProfileUiState())
    val uiState: StateFlow<ProfileUiState> = _uiState.asStateFlow()
    
    private val _currentUser = MutableStateFlow<User?>(null)
    val currentUser: StateFlow<User?> = _currentUser.asStateFlow()
    
    init {
        loadCurrentUser()
    }
    
    private fun loadCurrentUser() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            val user = userRepository.getCurrentUser()
            _currentUser.value = user
            
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                name = user?.name ?: "",
                bio = user?.bio ?: "",
                gender = user?.gender ?: Gender.OTHER,
                birthDate = user?.birthDate ?: Date(),
                interests = user?.interests ?: emptyList(),
                profileImageUrl = user?.profileImageUrl ?: ""
            )
        }
    }
    
    fun updateName(name: String) {
        _uiState.value = _uiState.value.copy(name = name)
    }
    
    fun updateBio(bio: String) {
        _uiState.value = _uiState.value.copy(bio = bio)
    }
    
    fun updateGender(gender: Gender) {
        _uiState.value = _uiState.value.copy(gender = gender)
    }
    
    fun updateBirthDate(birthDate: Date) {
        _uiState.value = _uiState.value.copy(birthDate = birthDate)
    }
    
    fun updateInterests(interests: List<String>) {
        _uiState.value = _uiState.value.copy(interests = interests)
    }
    
    fun addInterest(interest: String) {
        val currentInterests = _uiState.value.interests.toMutableList()
        if (!currentInterests.contains(interest) && interest.isNotBlank()) {
            currentInterests.add(interest)
            _uiState.value = _uiState.value.copy(interests = currentInterests)
        }
    }
    
    fun removeInterest(interest: String) {
        val currentInterests = _uiState.value.interests.toMutableList()
        currentInterests.remove(interest)
        _uiState.value = _uiState.value.copy(interests = currentInterests)
    }
    
    fun uploadProfileImage(imageUri: Uri) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isUploadingImage = true, error = null)
            
            val result = userRepository.uploadProfileImage(imageUri)
            
            if (result.isSuccess) {
                val imageUrl = result.getOrThrow()
                _uiState.value = _uiState.value.copy(
                    isUploadingImage = false,
                    profileImageUrl = imageUrl
                )
            } else {
                _uiState.value = _uiState.value.copy(
                    isUploadingImage = false,
                    error = result.exceptionOrNull()?.message ?: "Image upload failed"
                )
            }
        }
    }
    
    fun saveProfile() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isSaving = true, error = null)
            
            val currentState = _uiState.value
            val currentUserValue = _currentUser.value
            
            if (currentUserValue == null) {
                // Create new user
                val newUser = User(
                    name = currentState.name,
                    bio = currentState.bio,
                    gender = currentState.gender,
                    birthDate = currentState.birthDate,
                    interests = currentState.interests,
                    profileImageUrl = currentState.profileImageUrl
                )
                
                val result = userRepository.createUser(newUser)
                
                if (result.isSuccess) {
                    _currentUser.value = result.getOrThrow()
                    _uiState.value = _uiState.value.copy(
                        isSaving = false,
                        message = "Profile created successfully"
                    )
                } else {
                    _uiState.value = _uiState.value.copy(
                        isSaving = false,
                        error = result.exceptionOrNull()?.message ?: "Failed to create profile"
                    )
                }
            } else {
                // Update existing user
                val updatedUser = currentUserValue.copy(
                    name = currentState.name,
                    bio = currentState.bio,
                    gender = currentState.gender,
                    birthDate = currentState.birthDate,
                    interests = currentState.interests,
                    profileImageUrl = currentState.profileImageUrl
                )
                
                val result = userRepository.updateUser(updatedUser)
                
                if (result.isSuccess) {
                    _currentUser.value = result.getOrThrow()
                    _uiState.value = _uiState.value.copy(
                        isSaving = false,
                        message = "Profile updated successfully"
                    )
                } else {
                    _uiState.value = _uiState.value.copy(
                        isSaving = false,
                        error = result.exceptionOrNull()?.message ?: "Failed to update profile"
                    )
                }
            }
        }
    }
    
    fun toggleVisibility() {
        viewModelScope.launch {
            val currentUser = _currentUser.value ?: return@launch
            val newVisibility = !currentUser.isVisible
            
            val result = userRepository.setUserVisibility(newVisibility)
            
            if (result.isSuccess) {
                _currentUser.value = currentUser.copy(isVisible = newVisibility)
            } else {
                _uiState.value = _uiState.value.copy(
                    error = result.exceptionOrNull()?.message ?: "Failed to update visibility"
                )
            }
        }
    }
    
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
    
    fun clearMessage() {
        _uiState.value = _uiState.value.copy(message = null)
    }
}

data class ProfileUiState(
    val isLoading: Boolean = false,
    val isSaving: Boolean = false,
    val isUploadingImage: Boolean = false,
    val name: String = "",
    val bio: String = "",
    val gender: Gender = Gender.OTHER,
    val birthDate: Date = Date(),
    val interests: List<String> = emptyList(),
    val profileImageUrl: String = "",
    val error: String? = null,
    val message: String? = null
)
