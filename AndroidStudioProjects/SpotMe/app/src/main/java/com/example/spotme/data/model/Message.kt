package com.example.spotme.data.model

import java.util.Date

data class Message(
    val id: String = "",
    val chatId: String = "",
    val senderId: String = "",
    val receiverId: String = "",
    val content: String = "",
    val imageUrl: String = "",
    val type: MessageType = MessageType.TEXT,
    val timestamp: Date = Date(),
    val isRead: Boolean = false
)

enum class MessageType {
    TEXT, IMAGE, EMOJI
}

data class Chat(
    val id: String = "",
    val participants: List<String> = emptyList(),
    val lastMessage: Message? = null,
    val lastMessageTimestamp: Date = Date(),
    val createdAt: Date = Date()
) {
    fun getOtherParticipantId(currentUserId: String): String? {
        return participants.find { it != currentUserId }
    }
}
